<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bajaj Motorcycles - Enhanced Mega Menu</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              accent: "#326AD2",
              "bajaj-red": "#E31937",
              "active-text": "#222222",
            },
          },
        },
      };
    </script>
    <!-- <link rel="stylesheet" href="css/styles.css" /> -->
    <style>
      /* Top Bar Responsive Styles */
      .top-bar {
        padding-left: 150px;
        padding-right: 150px;
      }

      /* Transparent top bar */
      .top-bar-transparent {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      /* Top bar content responsive adjustments */
      .top-bar-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .top-bar-left {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .top-bar-right {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      /* Top bar text responsive sizing */
      .top-bar-text {
        font-size: 0.875rem;
        font-weight: bold;
        color: #fafafa;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* Top bar logo responsive sizing */
      .top-bar-logo {
        height: 2rem;
        width: auto;
        flex-shrink: 0;
      }

      /* Top bar icon responsive sizing */
      .top-bar-icon {
        width: 23px;
        height: 18px;
        flex-shrink: 0;
      }

      .floating-navbar {
        margin: 0 auto;
        margin-left: 150px;
        margin-right: 150px;
        height: 110px;
        padding-left: 0;
        padding-right: 0;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .nav-bg {
        background: linear-gradient(to bottom, #ffffff 0%, #ebebeb 100%);
      }

      .model-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 1rem;
      }

      .category-btn.active {
        color: #222222;
        text-decoration: underline;
        text-underline-offset: 4px;
      }

      .tab-btn.active {
        color: #222222;
        border-bottom: 2px solid #222222;
      }

      .mobile-model-item:hover {
        background-color: #f9fafb;
      }

      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }

      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }

      .media-dropdown {
        min-width: 200px;
      }

      /* Fixed overlay stacking context */
      #mobile-overlay {
        z-index: 40;
        /* Below category detail */
      }

      #mobile-category-detail {
        z-index: 60;
        /* Above overlay */
      }

      /* Improved mobile navigation */
      .mobile-nav-section {
        transition: all 0.3s ease;
      }

      /* Mega Menu Fixed Height */
      .mega-menu-container {
        height: 500px;
        /* Fixed height */
        overflow: hidden;
        /* Hide scrollbars for the container */
      }

      .mega-menu-scrollable {
        height: 100%;
        overflow-y: auto;
        /* Enable scrolling for content */
        scrollbar-width: thin;
        scrollbar-color: #0047ab #f1f1f1;
      }

      .mega-menu-scrollable::-webkit-scrollbar {
        width: 8px;
      }

      .mega-menu-scrollable::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      .mega-menu-scrollable::-webkit-scrollbar-thumb {
        background: #0047ab;
        border-radius: 4px;
      }

      .mega-menu-scrollable::-webkit-scrollbar-thumb:hover {
        background: #003380;
      }

      .categories-sidebar {
        height: 100%;
        overflow-y: auto;
      }

      .models-section {
        height: calc(100% - 50px);
        /* Account for tabs height */
      }

      /* Category heading styles */
      .category-heading {
        grid-column: 1 / -1;
        text-align: left;
        padding: 10px 0 5px;
        font-weight: 600;
        /* color: #0047ab; */
        margin-top: 15px;
        width: fit-content;
        /* border-bottom: 1px solid #e5e7eb; */
      }

      .category-heading:first-child {
        margin-top: 0;
      }
    </style>
  </head>

  <body class="bg-gray-50">
    <header class="header-overlay fixed top-0 left-0 right-0 z-50">
      <!-- Top Bar -->
      <div class="bg-gray-600 py-2 top-bar">
        <div class="top-bar-content">
          <div class="top-bar-left">
            <img
              src="assets/golcha-logo.png"
              alt="golcha_logo"
              class="top-bar-logo"
            />
            <span class="top-bar-text"
              >GOLCHHA GROUP WITH LEGACY OF 100 YEAR</span
            >
          </div>
          <div class="top-bar-right">
            <img src="./assets/globe.png" alt="globe" class="top-bar-icon" />
            <span class="top-bar-text">International website</span>
          </div>
        </div>
      </div>

      <!-- Main Navbar -->
      <nav class="nav-bg floating-navbar bg-white my-4 relative">
        <div class="px-6">
          <div class="flex justify-between items-center h-[110px]">
            <!-- Mobile Menu Button -->
            <button id="mobile-menu-btn" class="lg:hidden p-2">
              <i class="fas fa-bars text-xl text-gray-700"></i>
            </button>

            <!-- Desktop Navigation - Left Side -->
            <div class="hidden lg:flex items-center space-x-8">
              <!-- Motorcycles Dropdown -->
              <div class="relative group">
                <button class="flex items-center space-x-1 hover:text-accent">
                  <span>MOTORCYCLES</span>
                  <i class="fas fa-chevron-down text-xs"></i>
                </button>

                <!-- Mega Dropdown - Fixed Height Container -->
                <div
                  class="absolute top-full left-0 w-screen max-w-5xl bg-white shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 mega-menu-container"
                >
                  <div class="flex h-full">
                    <!-- Categories Sidebar - Scrollable -->
                    <div class="w-64 bg-gray-100 p-4 categories-sidebar">
                      <div class="space-y-2">
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent active"
                          data-category="pulsar"
                        >
                          PULSAR
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="dominar"
                        >
                          DOMINAR
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium over:text-accent"
                          data-category="avengers"
                        >
                          AVENGERS
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="discover"
                        >
                          DISCOVER
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="platina"
                        >
                          PLATINA
                        </button>
                      </div>
                    </div>

                    <!-- Models Section - Scrollable -->
                    <div class="mega-menu-scrollable flex-1 flex flex-col">
                      <div class="p-6">
                        <!-- Category Tabs -->
                        <div
                          id="tabs-container"
                          class="flex space-x-6 mb-4 text-sm boder-b-2"
                        >
                          <!-- Tabs will be dynamically generated -->
                        </div>

                        <!-- Models Content -->
                        <div
                          id="models-content"
                          class="model-grid gap-4 models-section"
                        >
                          <!-- Models will be populated by JavaScript -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <a href="#" class="hover:text-accent flex items-center"
                >SHOWROOMS</a
              >
              <a href="#" class="hover:text-accent flex items-center"
                >WORKSHOPS</a
              >
              <a href="#" class="hover:text-accent flex items-center">EVENTS</a>
            </div>

            <!-- Centered Logo -->
            <div class="flex-1 flex justify-center">
              <a href="/index.html" class="flex items-center">
                <img class="h-[72px] px-4" src="assets/logo.png" alt="logo" />
              </a>
            </div>

            <!-- Desktop Navigation - Right Side -->
            <div class="hidden lg:flex items-center space-x-8">
              <a href="#" class="hover:text-accent flex items-center"
                >BOOK TEST RIDE</a
              >
              <a href="/about.html" class="hover:text-accent flex items-center"
                >ABOUT US</a
              >
              <a href="#" class="hover:text-accent flex items-center">NEWS</a>

              <!-- Media Center Dropdown - Desktop -->
              <div class="relative group">
                <button class="flex items-center space-x-1 hover:text-accent">
                  <span>MEDIA CENTER</span>
                  <i class="fas fa-chevron-down text-xs"></i>
                </button>
                <div
                  class="absolute top-full left-0 w-48 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 media-dropdown"
                >
                  <div class="py-2">
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >ABOUT US</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >ANNOUNCEMENTS</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >EVENTS</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >BLOGS</a
                    >
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >DOWNLOAD CENTER</a
                    >
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >CONTACT US</a
                    >
                    <a
                      href="faqs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >FAQS</a
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- Mobile BIKES Button -->
            <div class="lg:hidden relative">
              <button
                id="mobile-bikes-btn"
                class="text-gray-700 font-medium flex items-center space-x-1"
              >
                <span>BIKES</span>
                <i class="fas fa-chevron-down text-xs"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile Menu Overlay - Fixed z-index -->
        <div
          id="mobile-overlay"
          class="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 hidden"
        ></div>

        <!-- Mobile Menu -->
        <div
          id="mobile-menu"
          class="lg:hidden mobile-nav-section fixed top-0 left-0 w-80 h-screen bg-white z-50 transform -translate-x-full transition-transform duration-300"
        >
          <div class="p-4 bg-white">
            <button
              id="close-mobile-menu"
              class="absolute top-4 right-4 text-xl text-gray-600"
            >
              <i class="fas fa-times"></i>
            </button>

            <!-- Mobile Menu Items -->
            <div class="mt-8 space-y-4">
              <!-- Mobile Motorcycles Dropdown -->
              <div class="mobile-dropdown">
                <button
                  class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2"
                >
                  <span>BIKES</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </button>
                <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="pulsar"
                  >
                    PULSAR
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="dominar"
                  >
                    DOMINAR
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="avengers"
                  >
                    AVENGERS
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="discover"
                  >
                    DISCOVER
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="platina"
                  >
                    PLATINA
                  </button>
                </div>
              </div>

              <a href="#" class="block py-2 font-medium">SHOWROOMS</a>
              <a href="#" class="block py-2 font-medium">WORKSHOPS</a>
              <a href="#" class="block py-2 font-medium">EVENTS</a>
              <a href="#" class="block py-2 font-medium">BOOK TEST RIDE</a>
              <a href="#" class="block py-2 font-medium">ABOUT US</a>
              <a href="#" class="block py-2 font-medium">NEWS</a>

              <!-- Media Center Dropdown - Mobile -->
              <div class="mobile-dropdown">
                <button
                  class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2"
                >
                  <span>MEDIA CENTER</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </button>
                <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >ABOUT US</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >ANNOUNCEMENTS</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >EVENTS</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600">BLOGS</a>
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >DOWNLOAD CENTER</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >CONTACT US</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600">FAQS</a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile Category Detail View - Fixed z-index -->
        <div
          id="mobile-category-detail"
          class="lg:hidden mobile-nav-section fixed top-0 left-0 w-80 h-screen bg-white z-50 transform -translate-x-full transition-transform duration-300"
        >
          <div class="p-4">
            <div class="flex items-center mb-4">
              <button id="back-to-categories" class="mr-3 text-gray-600">
                <i class="fas fa-chevron-left"></i>
              </button>
              <button
                id="close-category-detail"
                class="absolute top-4 right-4 text-xl text-gray-600"
              >
                <i class="fas fa-times"></i>
              </button>
              <span id="category-title" class="font-medium text-gray-800"
                >BIKES</span
              >
            </div>

            <!-- Category Tabs -->
            <div
              id="mobile-tabs-container"
              class="flex space-x-4 mb-4 text-sm border-b pb-2"
            >
              <!-- Tabs will be dynamically generated -->
            </div>

            <!-- Mobile Models List -->
            <div
              id="mobile-models-list"
              class="space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto scrollbar-hide"
            >
              <!-- Models will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Mobile BIKES Sheet - Same behavior as hamburger menu -->
        <div
          id="mobile-bikes-sheet"
          class="lg:hidden mobile-nav-section fixed top-0 right-0 w-80 h-screen bg-white z-50 transform translate-x-full transition-transform duration-300"
        >
          <div class="p-4 bg-white relative">
            <div class="flex items-center justify-between mb-4">
              <span
                id="mobile-bikes-header"
                class="font-medium text-gray-800 uppercase"
                >Bikes</span
              >
              <button id="close-bikes-sheet" class="text-xl text-gray-600">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <button
              id="back-to-bikes-categories"
              class="hidden items-center text-sm text-gray-600 mb-4"
            >
              <i class="fas fa-chevron-left mr-2"></i>
              Back
            </button>

            <!-- Categories List -->
            <div id="mobile-bikes-categories-list" class="space-y-1 mt-4">
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="pulsar"
              >
                <span>PULSAR</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="dominar"
              >
                <span>DOMINAR</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="avengers"
              >
                <span>AVENGERS</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="discover"
              >
                <span>DISCOVER</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="platina"
              >
                <span>PLATINA</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
            </div>

            <!-- Category Tabs (hidden initially) -->
            <div
              id="mobile-bikes-tabs-container"
              class="hidden flex-wrap gap-x-4 gap-y-2 mb-4 text-sm border-b pb-2"
            >
              <!-- Tabs will be dynamically generated -->
            </div>

            <!-- Mobile Bikes Models List (hidden initially) -->
            <div
              id="mobile-bikes-models-content"
              class="hidden space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto scrollbar-hide"
            >
              <!-- Models will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </nav>
    </header>

    <script>
      // Comprehensive motorcycle data with brand-specific categories
      const motorcycleData = {
        pulsar: {
          classic: [
            {
              name: "PULSAR 220F ABS",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P220F",
            },
            {
              name: "PULSAR 150 TD",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150TD",
            },
            {
              name: "PULSAR 150",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150",
            },
            {
              name: "PULSAR 125",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P125",
            },
          ],
          ns: [
            {
              name: "PULSAR NS400Z",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS400Z",
            },
            {
              name: "PULSAR NS 200 ABS FI",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200FI",
            },
            {
              name: "PULSAR NS 200 ABS",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200",
            },
            {
              name: "PULSAR NS 200",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200",
            },
            {
              name: "PULSAR NS 160 ABS",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160",
            },
            {
              name: "PULSAR NS160 FI DUAL ABS BS6",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160BS6",
            },
            {
              name: "PULSAR NS 125 BS6",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS125BS6",
            },
            {
              name: "PULSAR NS 125 FI BS6",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS125FIB",
            },
          ],
          n: [
            {
              name: "PULSAR N250",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N250",
            },
            {
              name: "PULSAR N160",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N160",
            },
          ],
        },
        dominar: {
          classic: [
            {
              name: "DOMINAR 400",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D400",
            },
            {
              name: "DOMINAR 250",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D250",
            },
          ],
        },
        avengers: {
          cruiser: [
            {
              name: "AVENGER CRUISE 220",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=AV220",
            },
            {
              name: "AVENGER STREET 160",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=AV160",
            },
          ],
        },
        discover: {
          commuter: [
            {
              name: "DISCOVER 125",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D125",
            },
            {
              name: "DISCOVER 110",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D110",
            },
          ],
        },
        platina: {
          commuter: [
            {
              name: "PLATINA 110",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=PL110",
            },
            {
              name: "PLATINA 100",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=PL100",
            },
          ],
        },
      };

      let currentCategory = "pulsar";
      let currentTab = "all";

      // DOM elements
      const mobileOverlay = document.getElementById("mobile-overlay");
      const mobileMenu = document.getElementById("mobile-menu");
      const mobileCategoryDetail = document.getElementById(
        "mobile-category-detail"
      );
      const mobileBikesSheet = document.getElementById("mobile-bikes-sheet");
      const mobileMenuBtn = document.getElementById("mobile-menu-btn");
      const mobileBikesBtn = document.getElementById("mobile-bikes-btn");
      const closeMobileMenu = document.getElementById("close-mobile-menu");
      const closeBikesSheet = document.getElementById("close-bikes-sheet");
      const backToCategories = document.getElementById("back-to-categories");
      const closeCategoryDetail = document.getElementById(
        "close-category-detail"
      );

      // Mobile Bikes Sheet Elements
      const mobileBikesHeader = document.getElementById("mobile-bikes-header");
      const backToBikesCategories = document.getElementById(
        "back-to-bikes-categories"
      );
      const mobileBikesCategoriesList = document.getElementById(
        "mobile-bikes-categories-list"
      );
      const mobileBikesTabsContainer = document.getElementById(
        "mobile-bikes-tabs-container"
      );
      const mobileBikesModelsContent = document.getElementById(
        "mobile-bikes-models-content"
      );

      // Function to format sub-category names
      function formatSubCategoryName(name) {
        if (name === "ns") return "NS";
        if (name === "n") return "N";
        if (name === "classic") return "Classic";
        if (name === "cruiser") return "Cruiser";
        if (name === "commuter") return "Commuter";
        return name.charAt(0).toUpperCase() + name.slice(1);
      }

      // Function to render tabs dynamically
      function renderTabs(category, isMobile = false, customContainer = null) {
        const tabsContainer = customContainer
          ? document.getElementById(customContainer)
          : isMobile
          ? document.getElementById("mobile-tabs-container")
          : document.getElementById("tabs-container");

        tabsContainer.innerHTML = "";

        // Get available tabs for this category
        const availableTabs = Object.keys(motorcycleData[category]).filter(
          (tab) => motorcycleData[category][tab].length > 0
        );

        // Always show "All" tab
        const allTab = document.createElement("button");
        allTab.className = `tab-btn ${
          currentTab === "all"
            ? "border-b-2 border-active-text font-medium active"
            : "text-active-text font-medium"
        } pb-1`;
        allTab.dataset.tab = "all";
        allTab.textContent = "All";
        tabsContainer.appendChild(allTab);

        // Create tabs for each available category
        availableTabs.forEach((tabName) => {
          const tab = document.createElement("button");
          tab.className = `tab-btn ${
            currentTab === tabName
              ? "border-b-2 border-active-text active font-medium"
              : "text-active-text font-medium"
          } pb-1`;
          tab.dataset.tab = tabName;
          tab.textContent = formatSubCategoryName(tabName);
          tabsContainer.appendChild(tab);
        });

        // Add event listeners to new tabs
        addTabEventListeners(isMobile);
      }

      // Function to render models
      function renderModels(
        category,
        tab,
        isMobile = false,
        customContainer = null
      ) {
        const container = customContainer
          ? document.getElementById(customContainer)
          : isMobile
          ? document.getElementById("mobile-models-list")
          : document.getElementById("models-content");

        let models = [];

        if (tab === "all") {
          // Create container HTML with category headings
          let containerHTML = "";

          // Get all sub-categories for this category
          const subCategories = Object.keys(motorcycleData[category]);

          // Iterate through each sub-category
          for (const subCategory of subCategories) {
            const subCategoryModels =
              motorcycleData[category][subCategory] || [];
            if (subCategoryModels.length === 0) continue;

            // Add category heading
            if (isMobile) {
              containerHTML += `
                            <div class="mt-4 mb-2 pl-3">
                                <div class="text-sm font-semibold text-accent border-b border-gray-200 pb-1">
                                    ${formatSubCategoryName(subCategory)}
                                </div>
                            </div>
                        `;
            } else {
              containerHTML += `
                            <div class="category-heading flex items-center gap-2 p-2">
                                <div class="w-1 h-6 bg-accent rounded-r-sm"></div>
                                    <span class="font-semibold text-black text-base">${formatSubCategoryName(
                                      subCategory
                                    )}</span>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="w-4 h-4 text-black ml-auto"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                        stroke-width="2"
                                    >
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                    </svg>
                            </div>
                        `;
            }

            // Add models for this sub-category
            subCategoryModels.forEach((model) => {
              if (isMobile) {
                containerHTML += `
                                <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                                    <img src="${model.image}" alt="${model.name}" class="w-16 h-10 object-cover rounded">
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-800">${model.name}</div>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            `;
              } else {
                containerHTML += `
                                <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                    <img src="${model.image}" alt="${model.name}" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                    <p class="text-xs font-medium text-gray-800 mb-1">${model.name}</p>
                                </div>
                            `;
              }
            });
          }

          container.innerHTML = containerHTML;
        } else {
          // For specific tabs, just show models without headings
          models = motorcycleData[category][tab] || [];

          if (isMobile) {
            container.innerHTML = models
              .map(
                (model) => `
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="${model.image}" alt="${model.name}" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">${model.name}</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    `
              )
              .join("");
          } else {
            container.innerHTML = models
              .map(
                (model) => `
                        <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                            <img src="${model.image}" alt="${model.name}" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                            <p class="text-xs font-medium text-gray-800 mb-1">${model.name}</p>
                        </div>
                    `
              )
              .join("");
          }

          // Show message if no models found
          if (models.length === 0) {
            container.innerHTML = `
                        <div class="${
                          isMobile
                            ? "text-center py-8 text-gray-500"
                            : "col-span-full text-center py-8 text-gray-500"
                        }">
                            No models available in this category
                        </div>
                    `;
          }
        }
      }

      // Initialize with default category and tab
      renderTabs(currentCategory);
      renderModels(currentCategory, currentTab);

      // Add tab event listeners
      function addTabEventListeners(isMobile = false) {
        const selector = isMobile
          ? "#mobile-tabs-container .tab-btn"
          : "#tabs-container .tab-btn";
        const tabs = document.querySelectorAll(selector);

        tabs.forEach((tab) => {
          tab.addEventListener("click", () => {
            const tabName = tab.dataset.tab;
            currentTab = tabName;

            // Update active tab styling
            tabs.forEach((t) => {
              t.classList.remove(
                "text-accent",
                "border-b-2",
                "border-accent",
                "active"
              );
              t.classList.add("text-gray-500");
            });
            tab.classList.remove("text-gray-500");
            tab.classList.add(
              "text-accent",
              "border-b-2",
              "border-accent",
              "active"
            );

            renderModels(currentCategory, tabName, isMobile);
          });
        });
      }

      // Mobile menu functionality
      // Open mobile menu
      mobileMenuBtn.addEventListener("click", () => {
        mobileMenu.classList.remove("-translate-x-full");
        mobileOverlay.classList.remove("hidden");
        document.body.style.overflow = "hidden";
      });

      // Close mobile menu
      function closeMobileMenuFunc() {
        mobileMenu.classList.add("-translate-x-full");
        mobileCategoryDetail.classList.add("-translate-x-full");
        mobileBikesSheet.classList.add("translate-x-full");
        mobileOverlay.classList.add("hidden");
        document.body.style.overflow = "auto";
      }

      closeMobileMenu.addEventListener("click", closeMobileMenuFunc);
      mobileOverlay.addEventListener("click", closeMobileMenuFunc);

      // Mobile dropdown functionality
      document.querySelectorAll(".mobile-dropdown-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const content = btn.nextElementSibling;
          const icon = btn.querySelector("i");

          if (content.classList.contains("hidden")) {
            content.classList.remove("hidden");
            icon.classList.remove("fa-chevron-right");
            icon.classList.add("fa-chevron-down");
          } else {
            content.classList.add("hidden");
            icon.classList.remove("fa-chevron-down");
            icon.classList.add("fa-chevron-right");
          }
        });
      });

      // Mobile category selection
      document.querySelectorAll(".mobile-category-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const category = btn.dataset.category;
          currentCategory = category;
          currentTab = "all";

          // Update category title
          document.getElementById(
            "category-title"
          ).textContent = `BIKES / ${category.toUpperCase()}`;

          // Render tabs and models for mobile
          renderTabs(category, true);
          renderModels(category, "all", true);

          // Hide overlay when showing model list
          mobileOverlay.classList.add("hidden");

          // Transition to model view
          mobileMenu.classList.add("-translate-x-full");
          setTimeout(() => {
            mobileCategoryDetail.classList.remove("-translate-x-full");
          }, 50);
        });
      });

      // Back to categories
      backToCategories.addEventListener("click", () => {
        mobileCategoryDetail.classList.add("-translate-x-full");
        setTimeout(() => {
          mobileMenu.classList.remove("-translate-x-full");
          mobileOverlay.classList.remove("hidden");
        }, 50);
      });

      // Close category detail
      closeCategoryDetail.addEventListener("click", closeMobileMenuFunc);

      // Desktop category switching
      document.querySelectorAll(".category-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const category = btn.dataset.category;
          currentCategory = category;
          currentTab = "all";

          // Remove active class from all buttons
          document.querySelectorAll(".category-btn").forEach((b) => {
            b.classList.remove("active");
          });

          // Add active class to clicked button
          btn.classList.add("active");

          // Render new tabs and models
          renderTabs(category);
          renderModels(category, "all");
        });
      });

      // Add click handlers for model items
      document.addEventListener("click", (e) => {
        if (
          e.target.closest(".model-item") ||
          e.target.closest(".mobile-model-item")
        ) {
          const modelElement = e.target.closest(
            ".model-item, .mobile-model-item"
          );
          const modelName =
            modelElement.querySelector("p, .text-sm").textContent;
          alert(
            `You selected: ${modelName}\n\nThis would typically navigate to the model details page.`
          );
        }
      });

      // Initialize tabs with event listeners
      addTabEventListeners();

      // Mobile BIKES sheet functionality
      // Function to reset bikes sheet to initial state (category list)
      const resetBikesSheet = () => {
        mobileBikesHeader.textContent = "BIKES";
        mobileBikesCategoriesList.classList.remove("hidden");
        backToBikesCategories.classList.add("hidden");
        mobileBikesTabsContainer.classList.add("hidden");
        mobileBikesModelsContent.classList.add("hidden");
      };

      // Open mobile bikes sheet
      mobileBikesBtn.addEventListener("click", () => {
        mobileBikesSheet.classList.remove("translate-x-full");
        mobileOverlay.classList.remove("hidden");
        document.body.style.overflow = "hidden";
      });

      // Close mobile bikes sheet
      const closeBikesSheetFunc = () => {
        mobileBikesSheet.classList.add("translate-x-full");
        if (
          document
            .getElementById("mobile-menu")
            .classList.contains("-translate-x-full")
        ) {
          mobileOverlay.classList.add("hidden");
          document.body.style.overflow = "auto";
        }
        setTimeout(resetBikesSheet, 300); // Reset after transition
      };
      closeBikesSheet.addEventListener("click", closeBikesSheetFunc);

      // Mobile bikes category selection from sheet
      document.querySelectorAll(".mobile-bikes-category-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const category = btn.dataset.category;
          currentCategory = category;
          currentTab = "all";

          // Update UI for model view
          mobileBikesHeader.textContent = category.toUpperCase();
          mobileBikesCategoriesList.classList.add("hidden");
          backToBikesCategories.classList.remove("hidden");
          mobileBikesTabsContainer.classList.remove("hidden");
          mobileBikesModelsContent.classList.remove("hidden");

          // Render content for selected category
          renderTabs(category, true, "mobile-bikes-tabs-container");
          renderModels(category, "all", true, "mobile-bikes-models-content");
        });
      });

      // Back to bikes categories
      backToBikesCategories.addEventListener("click", resetBikesSheet);

      // Mobile bikes tab switching
      document.addEventListener("click", (e) => {
        if (e.target.closest("#mobile-bikes-tabs-container .tab-btn")) {
          const tab = e.target.closest("#mobile-bikes-tabs-container .tab-btn");
          const tabName = tab.dataset.tab;
          currentTab = tabName;

          // Update active tab styling
          document
            .querySelectorAll("#mobile-bikes-tabs-container .tab-btn")
            .forEach((t) => {
              t.classList.remove("border-b-2", "border-accent", "active");
              t.classList.add("text-gray-500");
            });
          tab.classList.remove("text-gray-500");
          tab.classList.add(
            "text-accent",
            "border-b-2",
            "border-accent",
            "active"
          );

          renderModels(
            currentCategory,
            tabName,
            true,
            "mobile-bikes-models-content"
          );
        }
      });
    </script>
  </body>
</html>
