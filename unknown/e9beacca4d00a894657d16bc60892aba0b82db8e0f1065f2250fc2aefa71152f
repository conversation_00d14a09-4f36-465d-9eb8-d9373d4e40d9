<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PULSAR N250 Overview</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'highlight': 'highlight 1.5s ease-in-out infinite alternate',
                    },
                    keyframes: {
                        highlight: {
                            'from': { boxShadow: '0 0 5px rgba(0, 71, 171, 0.5)' },
                            'to': { boxShadow: '0 0 20px rgba(0, 71, 171, 0.8)' }
                        }
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .grid-container {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                grid-auto-rows: 1fr;
                gap: 0.5rem;
                height: 600px;
            }
            
            @media (min-width: 768px) {
                .grid-container {
                    grid-template-columns: repeat(6, 1fr);
                    grid-template-rows: repeat(16, 1fr);
                    height: 800px;
                    gap: 0.75rem;
                }
            }
            
            .grid-item {
                @apply relative overflow-hidden rounded-lg shadow-md transition-all duration-300 cursor-pointer;
            }
            
            .grid-item:hover {
                @apply -translate-y-1 shadow-xl;
            }
            
            .grid-item img {
                @apply w-full h-full object-cover transition-transform duration-500;
            }
            
            .grid-item:hover img {
                transform: scale(1.05);
            }
            
            .overlay {
                @apply absolute bottom-0 left-0 right-0 p-4 text-white;
                background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
            }
            
            .feature-highlight {
                animation: highlight 1.5s ease-in-out infinite alternate;
            }
            
            .modal-overlay {
                background: rgba(0, 0, 0, 0.85);
                backdrop-filter: blur(4px);
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <section>
        <div class="max-w-7xl mx-auto p-4 md:p-6">
            <!-- Header -->
            <div class="text-center mb-6 md:mb-8">
                <h1 class="text-2xl md:text-3xl font-bold text-gray-800 tracking-wider">
                    PULSAR N250 OVERVIEW
                </h1>
            </div>

            <!-- Static Grid Container -->
            <div class="grid-container w-full max-w-7xl mx-auto overflow-hidden mb-6 md:mb-8">
                <!-- Item 1: Engine -->
                <div class="grid-item col-span-2 row-span-4 md:col-span-2 md:row-span-8 feature-highlight" 
                     data-title="ALL-NEW REFINED ENGINE"
                     data-description="Experience the thrill of a powerful, refined engine designed for optimal performance and efficiency."
                     data-image="https://images.unsplash.com/photo-1558981403-c5f9899a28bc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80">
                    <img src="https://images.unsplash.com/photo-1558981403-c5f9899a28bc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Refined Engine">
                    <div class="overlay">
                        <div class="font-bold text-sm md:text-base">ALL-NEW REFINED ENGINE</div>
                        <div class="text-xs text-gray-300 mt-1 hidden group-hover:block">Experience the thrill of a powerful, refined engine designed for optimal performance and efficiency.</div>
                    </div>
                </div>

                <!-- Item 2: Exhaust -->
                <div class="grid-item col-span-2 row-span-2 md:col-span-4 md:row-span-4 md:col-start-3" 
                     data-title="SPORTY UNDERBELLY EXHAUST"
                     data-description="The sleek underbelly exhaust not only looks great but also contributes to better mass centralization."
                     data-image="https://images.unsplash.com/photo-1621330396168-4c2287dadc0a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80">
                    <img src="https://images.unsplash.com/photo-1621330396168-4c2287dadc0a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Sporty Exhaust">
                    <div class="overlay">
                        <div class="font-bold text-sm md:text-base">SPORTY UNDERBELLY EXHAUST</div>
                        <div class="text-xs text-gray-300 mt-1 hidden group-hover:block">The sleek underbelly exhaust not only looks great but also contributes to better mass centralization.</div>
                    </div>
                </div>

                <!-- Item 3: ABS -->
                <div class="grid-item col-span-1 row-span-3 md:col-span-2 md:row-span-6 md:col-start-3 md:row-start-5" 
                     data-title="DUAL CHANNEL ABS BRAKING TECHNOLOGY"
                     data-description="Enhanced safety with dual-channel ABS, providing superior braking control in all conditions."
                     data-image="https://images.unsplash.com/photo-1558980664-10e7170b5df9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80">
                    <img src="https://images.unsplash.com/photo-1558980664-10e7170b5df9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="ABS Braking">
                    <div class="overlay">
                        <div class="font-bold text-sm md:text-base">DUAL CHANNEL ABS</div>
                        <div class="text-xs text-gray-300 mt-1 hidden group-hover:block">Enhanced safety with dual-channel ABS, providing superior braking control in all conditions.</div>
                    </div>
                </div>

                <!-- Item 4: Colors -->
                <div class="grid-item col-span-1 row-span-3 md:col-span-2 md:row-span-6 md:col-start-5 md:row-start-5" 
                     data-title="RED, BLUE, BLACK"
                     data-description="Available in striking color options: fiery Red, vibrant Blue, and classic Black."
                     data-image="https://images.unsplash.com/photo-1558980664-2506fca6bfc2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80">
                    <img src="https://images.unsplash.com/photo-1558980664-2506fca6bfc2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Color Options">
                    <div class="overlay">
                        <div class="font-bold text-sm md:text-base">RED, BLUE, BLACK</div>
                        <div class="text-xs text-gray-300 mt-1 hidden group-hover:block">Available in striking color options: fiery Red, vibrant Blue, and classic Black.</div>
                    </div>
                </div>

                <!-- Item 5: Suspension -->
                <div class="grid-item col-span-2 row-span-4 md:col-span-2 md:row-span-8 md:row-start-9" 
                     data-title="BEST IN CLASS SUSPENSION"
                     data-description="Enjoy a smooth and comfortable ride with best-in-class suspension, designed to absorb bumps and offer stability."
                     data-image="https://images.unsplash.com/photo-1558980664-769d0f1e67d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80">
                    <img src="https://images.unsplash.com/photo-1558980664-769d0f1e67d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Suspension">
                    <div class="overlay">
                        <div class="font-bold text-sm md:text-base">BEST IN CLASS SUSPENSION</div>
                        <div class="text-xs text-gray-300 mt-1 hidden group-hover:block">Enjoy a smooth and comfortable ride with best-in-class suspension, designed to absorb bumps and offer stability.</div>
                    </div>
                </div>

                <!-- Item 6: Torque -->
                <div class="grid-item col-span-1 row-span-3 md:col-span-2 md:row-span-6 md:col-start-3 md:row-start-11" 
                     data-title="TORQUE ON DEMAND"
                     data-description="Unleash instant power with torque on demand, ensuring quick acceleration and responsive handling."
                     data-image="https://images.unsplash.com/photo-1558981001-5864b3250a69?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80">
                    <img src="https://images.unsplash.com/photo-1558981001-5864b3250a69?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Torque">
                    <div class="overlay">
                        <div class="font-bold text-sm md:text-base">TORQUE ON DEMAND</div>
                        <div class="text-xs text-gray-300 mt-1 hidden group-hover:block">Unleash instant power with torque on demand, ensuring quick acceleration and responsive handling.</div>
                    </div>
                </div>

                <!-- Item 7: Dual Tone -->
                <div class="grid-item col-span-1 row-span-3 md:col-span-2 md:row-span-6 md:col-start-5 md:row-start-11" 
                     data-title="DUAL TONE"
                     data-description="A sophisticated dual-tone finish adds a premium look and feel to the motorcycle."
                     data-image="https://images.unsplash.com/photo-1558981001-792f6c0d5068?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80">
                    <img src="https://images.unsplash.com/photo-1558981001-792f6c0d5068?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Dual Tone">
                    <div class="overlay">
                        <div class="font-bold text-sm md:text-base">DUAL TONE</div>
                        <div class="text-xs text-gray-300 mt-1 hidden group-hover:block">A sophisticated dual-tone finish adds a premium look and feel to the motorcycle.</div>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="text-center">
                <h2 class="text-xl md:text-2xl font-bold text-gray-800 mb-4">
                    GIVE A TEST RIDE
                </h2>
                <p class="text-gray-600 text-sm mb-6 max-w-md mx-auto">
                    Experience the thrill of the Bajaj Pulsar N250 - a perfect blend
                    of power, technology, and style. Contact us and book your test
                    ride today.
                </p>
                <button class="bg-black text-white px-6 md:px-8 py-2 md:py-3 rounded-md font-semibold hover:bg-gray-800 transition-colors duration-300">
                    BOOK TEST RIDE
                </button>
            </div>
        </div>
    </section>
    
    <!-- Image Modal -->
    <div id="imageModal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
        <div class="modal-overlay absolute inset-0" id="modalOverlay"></div>
        <div class="relative bg-white rounded-xl overflow-hidden z-10 w-11/12 max-w-4xl">
            <button id="closeModal" class="absolute top-4 right-4 bg-black/30 hover:bg-black/50 text-white rounded-full w-10 h-10 flex items-center justify-center z-20">
                <i class="fas fa-times"></i>
            </button>
            <div class="relative">
                <img id="modalImage" src="" alt="Feature Preview" class="w-full max-h-[70vh] object-contain">
                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 pt-8">
                    <h3 id="modalTitle" class="text-xl font-bold text-white"></h3>
                    <p id="modalDescription" class="text-gray-200 mt-1"></p>
                </div>
            </div>
            <div class="p-4 flex justify-center">
                <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium">
                    <i class="fas fa-calendar-alt mr-2"></i>Book Your Test Ride
                </button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const gridItems = document.querySelectorAll('.grid-item');
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalTitle = document.getElementById('modalTitle');
            const modalDescription = document.getElementById('modalDescription');
            const modalOverlay = document.getElementById('modalOverlay');
            const closeModal = document.getElementById('closeModal');
            
            // Open modal on grid item click
            gridItems.forEach(item => {
                item.addEventListener('click', function() {
                    const imgSrc = this.getAttribute('data-image');
                    const title = this.getAttribute('data-title');
                    const description = this.getAttribute('data-description');
                    
                    modalImage.src = imgSrc;
                    modalTitle.textContent = title;
                    modalDescription.textContent = description;
                    
                    modal.classList.remove('hidden');
                    document.body.classList.add('overflow-hidden');
                });
            });
            
            // Close modal
            function closeImageModal() {
                modal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
            
            closeModal.addEventListener('click', closeImageModal);
            modalOverlay.addEventListener('click', closeImageModal);
            
            // Close modal with ESC key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                    closeImageModal();
                }
            });
        });
    </script>
</body>
</html>