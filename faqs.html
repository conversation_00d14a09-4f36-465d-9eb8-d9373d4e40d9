<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frequently Asked Questions</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        'primary-dark': '#2563EB'
                    }
                }
            }
        }
    </script>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="max-w-6xl mx-auto p-6">
        <!-- Main Heading -->
        <h1 class="text-3xl font-bold text-center text-gray-900 mb-8">Frequently Asked Question</h1>

        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar -->
            <div class="lg:w-1/4">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Select question type</h2>
                    <ul class="space-y-3">
                        <li>
                            <button
                                class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700"
                                data-category="bajaj">
                                BAJAJ Hub Related
                            </button>
                        </li>
                        <li>
                            <button
                                class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700"
                                data-category="app">
                                App-related Issues
                            </button>
                        </li>
                        <li>
                            <button
                                class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700 bg-gray-100"
                                data-category="ota">
                                OTA (Over-The-Air) Issues
                            </button>
                        </li>
                        <li>
                            <button
                                class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700"
                                data-category="privacy">
                                Privacy-related Issues
                            </button>
                        </li>
                        <li>
                            <button
                                class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700"
                                data-category="positioning">
                                Positioning and Navigation Issues
                            </button>
                        </li>
                        <li>
                            <button
                                class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700"
                                data-category="binding">
                                Binding Issues
                            </button>
                        </li>
                        <li>
                            <button
                                class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700"
                                data-category="vin">
                                VIN-related Issues
                            </button>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:w-3/4">
                <div class="bg-white rounded-lg shadow-sm">
                    <!-- FAQ Items -->
                    <div class="faq-content" data-category="ota">
                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="true">
                                <span class="text-primary font-medium">The previous owner still binding the vehicle, how
                                    can I fix this issue?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transform rotate-180 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6">
                                <p class="text-gray-600 leading-relaxed">
                                    Please submit an unbinding request through the App (The unbinding request should
                                    include the complete VIN number, the user's real name, personal phone number, etc.)
                                </p>
                            </div>
                        </div>

                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="true">
                                <span class="text-primary font-medium">Can I bind many vehicles to the APP?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transform rotate-180 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6">
                                <p class="text-gray-600 leading-relaxed">
                                    Yes, you can bind many vehicles and customers can switch vehicles in the garage.
                                </p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="true">
                                <span class="text-primary font-medium">Can I unbind the vehicle?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transform rotate-180 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6">
                                <p class="text-gray-600 leading-relaxed">
                                    The customer can unbind the vehicle in the APP Once unbound, the information about
                                    the vehicle would be deleted from the server, and can't be retrieved if can't the
                                    customer can unbind the vehicle in the APP Once unbound, the information about the
                                    vehicle would be deleted from the server and can't be retrieved if can't.
                                </p>
                                <p class="text-gray-600 leading-relaxed mt-2">
                                    Question can click the "feedback" in the APP to ask for help from the BAJAJ HUB
                                    team.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Other category content (hidden by default) -->
                    <!-- BAJAJ Hub Related -->
                    <div class="faq-content hidden" data-category="bajaj">
                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">How do I connect my vehicle to BAJAJ Hub?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    To connect your vehicle to BAJAJ Hub, ensure your vehicle is compatible and follow
                                    the setup instructions in the mobile app. You'll need to pair your device via
                                    Bluetooth and complete the initial configuration.
                                </p>
                            </div>
                        </div>

                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">What features are available in BAJAJ Hub?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    BAJAJ Hub offers vehicle tracking, remote diagnostics, maintenance alerts, trip
                                    history, fuel efficiency monitoring, and security features like anti-theft alerts.
                                </p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">Is BAJAJ Hub compatible with all BAJAJ
                                    vehicles?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    BAJAJ Hub is compatible with select BAJAJ vehicle models manufactured after 2020.
                                    Please check the compatibility list in the app or contact support for specific model
                                    information.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- App-related Issues -->
                    <div class="faq-content hidden" data-category="app">
                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">The app is not connecting to my vehicle. What
                                    should I do?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    First, ensure Bluetooth is enabled on your phone and the vehicle is in pairing mode.
                                    Try restarting both the app and your phone. If the issue persists, clear the app
                                    cache or reinstall the application.
                                </p>
                            </div>
                        </div>

                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">How do I update the BAJAJ Hub app?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    You can update the app through Google Play Store (Android) or App Store (iOS).
                                    Enable automatic updates to ensure you always have the latest version with new
                                    features and bug fixes.
                                </p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">Why is the app consuming too much battery?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    High battery consumption may be due to continuous GPS tracking or background sync.
                                    You can optimize battery usage by adjusting location settings and disabling
                                    unnecessary background activities in the app settings.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Privacy-related Issues -->
                    <div class="faq-content hidden" data-category="privacy">
                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">What data does BAJAJ Hub collect?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    BAJAJ Hub collects vehicle performance data, location information, usage patterns,
                                    and diagnostic information to provide personalized services. All data is encrypted
                                    and stored securely according to privacy regulations.
                                </p>
                            </div>
                        </div>

                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">Can I delete my personal data?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    Yes, you can request deletion of your personal data by contacting our support team
                                    or using the data deletion option in the app settings. Please note that some data
                                    may be retained for legal compliance purposes.
                                </p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">Is my location data shared with third
                                    parties?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    Your location data is not shared with third parties without your explicit consent.
                                    We only use location data to provide navigation services and emergency assistance
                                    features within the app.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Positioning and Navigation Issues -->
                    <div class="faq-content hidden" data-category="positioning">
                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">GPS location is not accurate. How can I fix
                                    this?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    Ensure GPS is enabled on your device and you have a clear view of the sky. Poor
                                    weather conditions or being in enclosed areas can affect GPS accuracy. Try
                                    restarting the app or recalibrating your device's compass.
                                </p>
                            </div>
                        </div>

                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">Navigation is not working properly. What should I
                                    do?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    Check your internet connection and ensure location services are enabled. Update the
                                    app to the latest version and clear the app cache. If issues persist, try using the
                                    navigation feature in an open area with good GPS signal.
                                </p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">Can I use navigation offline?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    Limited offline navigation is available for previously downloaded routes. For full
                                    navigation features including real-time traffic updates and route optimization, an
                                    internet connection is required.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Binding Issues -->
                    <div class="faq-content hidden" data-category="binding">
                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">How do I bind a new vehicle to my account?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    To bind a new vehicle, go to the "Add Vehicle" section in the app, enter your
                                    vehicle's VIN number, and follow the verification process. You may need to provide
                                    ownership documents for verification.
                                </p>
                            </div>
                        </div>

                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">Vehicle binding failed. What could be the
                                    reason?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    Binding may fail due to incorrect VIN entry, vehicle already bound to another
                                    account, or network connectivity issues. Verify the VIN number and ensure you have a
                                    stable internet connection during the binding process.
                                </p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">How many vehicles can I bind to one
                                    account?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    You can bind up to 5 vehicles to a single BAJAJ Hub account. If you need to bind
                                    more vehicles, please contact our support team for assistance with enterprise
                                    solutions.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- VIN-related Issues -->
                    <div class="faq-content hidden" data-category="vin">
                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">Where can I find my vehicle's VIN number?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    The VIN number can be found on your vehicle registration document, insurance papers,
                                    or physically on the vehicle frame. For motorcycles, it's typically located on the
                                    steering head or frame near the engine.
                                </p>
                            </div>
                        </div>

                        <div class="faq-item border-b border-gray-200">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">VIN number is not being accepted. What should I
                                    do?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    Double-check that you've entered the VIN correctly, avoiding confusion between
                                    similar characters (0 vs O, 1 vs I). Ensure your vehicle model is compatible with
                                    BAJAJ Hub. Contact support if the issue persists.
                                </p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <button
                                class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                                data-expanded="false">
                                <span class="text-primary font-medium">Can I change the VIN number after binding?</span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-answer px-6 pb-6" style="display: none;">
                                <p class="text-gray-600 leading-relaxed">
                                    VIN numbers cannot be changed once a vehicle is bound to your account for security
                                    reasons. If you need to correct a VIN, you'll need to unbind the vehicle and bind it
                                    again with the correct information.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Section -->
                <div class="mt-8 bg-white rounded-lg shadow-sm p-6">
                    <p class="text-gray-700 mb-4">
                        <strong>Didn't find the answer to your questions?</strong><br>
                        Contact us directly.
                    </p>
                    <button
                        class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-md transition-colors duration-200">
                        Contact Info
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const categoryButtons = document.querySelectorAll('.category-btn');
            const faqContents = document.querySelectorAll('.faq-content');

            categoryButtons.forEach(button => {
                button.addEventListener('click', function () {
                    const category = this.getAttribute('data-category');

                    // Remove active class from all buttons
                    categoryButtons.forEach(btn => btn.classList.remove('bg-gray-100'));

                    // Add active class to clicked button
                    this.classList.add('bg-gray-100');

                    // Hide all FAQ content
                    faqContents.forEach(content => content.classList.add('hidden'));

                    // FIXED: Use specific selector for content sections only
                    const targetContent = document.querySelector(`.faq-content[data-category="${category}"]`);
                    if (targetContent) {
                        targetContent.classList.remove('hidden');
                    }
                });
            });

            // FAQ accordion functionality
            const faqQuestions = document.querySelectorAll('.faq-question');

            faqQuestions.forEach(question => {
                question.addEventListener('click', function () {
                    const faqItem = this.closest('.faq-item');
                    const answer = faqItem.querySelector('.faq-answer');
                    const icon = this.querySelector('.faq-icon');
                    const isExpanded = this.getAttribute('data-expanded') === 'true';

                    if (isExpanded) {
                        // Collapse
                        answer.style.display = 'none';
                        icon.classList.remove('rotate-180');
                        this.setAttribute('data-expanded', 'false');
                    } else {
                        // Expand
                        answer.style.display = 'block';
                        icon.classList.add('rotate-180');
                        this.setAttribute('data-expanded', 'true');
                    }
                });
            });
        });
    </script>
</body>

</html>