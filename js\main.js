// Main Application Module
import { CarouselManager } from "./carousel.js";
import { NavbarManager } from "./navbar.js";

class BajajApp {
  constructor() {
    this.carouselManager = null;
    this.navbarManager = null;
  }

  // Initialize the application
  async initialize() {
    // Initialize carousel
    this.carouselManager = new CarouselManager();
    await this.carouselManager.initialize();

    // Initialize navbar (loads data when needed)
    this.navbarManager = new NavbarManager();
    this.navbarManager.initialize();

    // Make navbar manager globally available for HTML onclick handlers
    window.navbarManager = this.navbarManager;
  }
}

// Initialize the application when DOM is loaded
document.addEventListener("DOMContentLoaded", async function () {
  const app = new BajajApp();
  await app.initialize();
});

// Export for potential module usage
export default BajajApp;

// Hero Carousel Functionality
document.addEventListener("DOMContentLoaded", () => {
  // Hero Carousel
  const heroCarousel = {
    slides: document.querySelectorAll(".carousel-slide"),
    indicators: document.querySelectorAll(".carousel-indicators .indicator"),
    prevBtn: document.querySelector(".carousel-control.prev"),
    nextBtn: document.querySelector(".carousel-control.next"),
    currentSlide: 0,
    interval: null,

    init() {
      console.log("Initializing hero carousel...");
      console.log("Slides found:", this.slides.length);
      console.log("Indicators found:", this.indicators.length);
      console.log("Prev button:", this.prevBtn);
      console.log("Next button:", this.nextBtn);

      if (this.prevBtn) {
        this.prevBtn.addEventListener("click", (e) => {
          e.preventDefault();
          console.log("Previous button clicked");
          this.stopAutoPlay();
          this.prevSlide();
          this.startAutoPlay();
        });
      }

      if (this.nextBtn) {
        this.nextBtn.addEventListener("click", (e) => {
          e.preventDefault();
          console.log("Next button clicked");
          this.stopAutoPlay();
          this.nextSlide();
          this.startAutoPlay();
        });
      }

      this.indicators.forEach((indicator, index) => {
        indicator.addEventListener("click", (e) => {
          e.preventDefault();
          console.log("Indicator clicked:", index);
          this.stopAutoPlay();
          this.goToSlide(index);
          this.startAutoPlay();
        });
      });

      // Add hover pause functionality
      const carouselContainer = document.querySelector(".hero-carousel");
      if (carouselContainer) {
        carouselContainer.addEventListener("mouseenter", () => {
          console.log("Mouse entered carousel - pausing autoplay");
          this.stopAutoPlay();
        });

        carouselContainer.addEventListener("mouseleave", () => {
          console.log("Mouse left carousel - resuming autoplay");
          this.startAutoPlay();
        });
      }

      // Start autoplay
      this.startAutoPlay();
      console.log("Hero carousel initialized successfully");
    },

    showSlide(index) {
      console.log("Showing slide:", index);
      this.slides.forEach((slide) => slide.classList.remove("active"));
      this.indicators.forEach((indicator) =>
        indicator.classList.remove("active")
      );

      if (this.slides[index]) {
        this.slides[index].classList.add("active");
      }
      if (this.indicators[index]) {
        this.indicators[index].classList.add("active");
      }
      this.currentSlide = index;
    },

    nextSlide() {
      const next = (this.currentSlide + 1) % this.slides.length;
      this.showSlide(next);
    },

    prevSlide() {
      const prev =
        (this.currentSlide - 1 + this.slides.length) % this.slides.length;
      this.showSlide(prev);
    },

    goToSlide(index) {
      this.showSlide(index);
    },

    startAutoPlay() {
      this.stopAutoPlay(); // Clear any existing interval
      this.interval = setInterval(() => {
        console.log("Auto-advancing to next slide");
        this.nextSlide();
      }, 4000); // Changed to 4 seconds for faster auto-scroll
      console.log("Autoplay started");
    },

    stopAutoPlay() {
      if (this.interval) {
        clearInterval(this.interval);
        this.interval = null;
        console.log("Autoplay stopped");
      }
    },
  };

  // Bike Carousel Functionality
  const bikeCarousel = {
    brandSections: document.querySelectorAll(".bike-brand-section"),
    brandButtons: document.querySelectorAll(".brand-btn"),
    prevBtn: document.querySelector(".bike-carousel-controls .prev-btn"),
    nextBtn: document.querySelector(".bike-carousel-controls .next-btn"),
    currentBrand: 0,

    init() {
      this.brandButtons.forEach((btn, index) => {
        btn.addEventListener("click", () => this.switchBrand(index));
      });
      this.prevBtn.addEventListener("click", () => this.prevBrand());
      this.nextBtn.addEventListener("click", () => this.nextBrand());
    },

    switchBrand(index) {
      this.brandButtons.forEach((btn) => btn.classList.remove("active"));
      this.brandSections.forEach((section) =>
        section.classList.remove("active")
      );

      this.brandButtons[index].classList.add("active");
      this.brandSections[index].classList.add("active");
      this.currentBrand = index;
    },

    nextBrand() {
      const next = (this.currentBrand + 1) % this.brandSections.length;
      this.switchBrand(next);
    },

    prevBrand() {
      const prev =
        (this.currentBrand - 1 + this.brandSections.length) %
        this.brandSections.length;
      this.switchBrand(prev);
    },
  };

  // Color Switcher for Bikes
  const colorSwitcher = {
    init() {
      document.querySelectorAll(".bike-card").forEach((card) => {
        const colorBtns = card.querySelectorAll(".color-btn");
        const bikeImage = card.querySelector(".bike-image img");

        colorBtns.forEach((btn) => {
          btn.addEventListener("click", () => {
            const color = btn.dataset.color;
            const bikeId = card.dataset.id;

            // Update active state
            colorBtns.forEach((b) => b.classList.remove("active"));
            btn.classList.add("active");

            // Update image based on color
            const newImagePath = `/assets/bikes/${
              bikeId.split("-")[0]
            }/${bikeId}-${color}.svg`;
            bikeImage.src = newImagePath;
          });
        });
      });
    },
  };

  // Initialize all components
  heroCarousel.init();
  bikeCarousel.init();
  colorSwitcher.init();
});
