<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Static Bajaj Experiences Carousel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .expreience-carousel-slide {
            transition: all 0.6s ease;
        }

        .expreience-slide-image {
            transition: transform 0.3s ease;
        }

        .expreience-carousel-slide:hover .expreience-slide-image {
            transform: scale(1.05);
        }

        .expreience-explore-btn {
            transition: all 0.3s ease;
        }

        .explore-btn:hover {
            transform: translateY(-2px);
        }

        .expreience-nav-button {
            transition: all 0.3s ease;
        }

        .expreience-nav-button:hover {
            transform: translateY(-50%) scale(1.1);
        }

        .expreience-nav-button:active {
            transform: translateY(-50%) scale(0.95);
        }
    </style>
</head>

<body class="bg-gradient-to-br from-blue-50 to-blue-200 min-h-screen flex items-center justify-center p-5">
    <div class="w-full max-w-2xl mx-auto">
        <h1 class="text-center text-3xl font-bold text-slate-800 mb-12 tracking-widest uppercase">The Bajaj Experiences
        </h1>

        <div class="relative h-[600px] flex items-center justify-center">
            <div id="carousel" class="relative w-full h-full flex items-center justify-center">
                <!-- Slide 0 -->
                <div class="expreience-carousel-slide absolute w-80 h-96 rounded-3xl overflow-hidden shadow-2xl bg-white"
                    data-index="0">
                    <div class="relative w-full h-full overflow-hidden rounded-3xl">
                        <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=600&fit=crop&crop=center"
                            alt="Red Sports Motorcycle" class="expreience-slide-image w-full h-full object-cover">
                        <div
                            class="explore-btn-wrapper absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6 flex justify-center items-end hidden">
                            <button
                                class="expreience-explore-btn bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-full text-sm font-bold uppercase tracking-wide shadow-lg hover:shadow-xl">
                                Explore More
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Slide 1 -->
                <div class="expreience-carousel-slide absolute w-80 h-96 rounded-3xl overflow-hidden shadow-2xl bg-white"
                    data-index="1">
                    <div class="relative w-full h-full overflow-hidden rounded-3xl">
                        <img src="https://images.unsplash.com/photo-1609630875171-b1321377ee65?w=400&h=600&fit=crop&crop=center"
                            alt="Blue Commuter Bike" class="expreience-slide-image w-full h-full object-cover">
                        <div
                            class="explore-btn-wrapper absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6 flex justify-center items-end hidden">
                            <button
                                class="expreience-explore-btn bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-full text-sm font-bold uppercase tracking-wide shadow-lg hover:shadow-xl">
                                Explore More
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Slide 2 -->
                <div class="expreience-carousel-slide absolute w-80 h-96 rounded-3xl overflow-hidden shadow-2xl bg-white"
                    data-index="2">
                    <div class="relative w-full h-full overflow-hidden rounded-3xl">
                        <img src="https://images.unsplash.com/photo-1571068316344-75bc76f77890?w=400&h=600&fit=crop&crop=center"
                            alt="Cruiser Motorcycle" class="expreience-slide-image w-full h-full object-cover">
                        <div
                            class="explore-btn-wrapper absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6 flex justify-center items-end hidden">
                            <button
                                class="expreience-explore-btn bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-full text-sm font-bold uppercase tracking-wide shadow-lg hover:shadow-xl">
                                Explore More
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <button id="prevBtn"
                class="expreience-nav-button absolute top-1/2 -translate-y-1/2 -left-16 sm:-left-20 bg-white/90 hover:bg-white w-12 h-12 rounded-full shadow-lg text-xl font-bold text-slate-700 z-10">‹</button>

            <button id="nextBtn"
                class="expreience-nav-button absolute top-1/2 -translate-y-1/2 -right-16 sm:-right-20 bg-white/90 hover:bg-white w-12 h-12 rounded-full shadow-lg text-xl font-bold text-slate-700 z-10">›</button>

        </div>
    </div>

    <script>
        const slides = document.querySelectorAll('.expreience-carousel-slide');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        let current = 1; // center index

        const transformClasses = [
            "z-5 opacity-80 transform -translate-x-48 -translate-z-24 rotate-y-12 scale-90",  // left
            "z-10 transform translate-x-0 translate-z-0 scale-100",                          // center
            "z-5 opacity-80 transform translate-x-48 -translate-z-24 -rotate-y-12 scale-90"  // right
        ];

        function updateSlides() {
            slides.forEach((slide, i) => {
                slide.className = "expreience-carousel-slide absolute w-80 h-96 rounded-3xl overflow-hidden shadow-2xl bg-white";

                const btnWrapper = slide.querySelector('.explore-btn-wrapper');
                btnWrapper.classList.add('hidden');

                const relativeIndex = (i - current + slides.length) % slides.length;

                if (relativeIndex === 0) {
                    slide.className += " " + transformClasses[0];
                } else if (relativeIndex === 1) {
                    slide.className += " " + transformClasses[1];
                    btnWrapper.classList.remove('hidden'); // show explore button on center
                } else if (relativeIndex === 2) {
                    slide.className += " " + transformClasses[2];
                }
            }); 
        }

        prevBtn.addEventListener("click", () => {
            current = (current - 1 + slides.length) % slides.length;
            updateSlides();
        });

        nextBtn.addEventListener("click", () => {
            current = (current + 1) % slides.length;
            updateSlides();
        });

        updateSlides(); // initial render
    </script>
</body>

</html>